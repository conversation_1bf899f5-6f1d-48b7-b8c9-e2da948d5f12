/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  images: {
    domains: ['ermdevstoragedata.blob.core.windows.net'],
  },
  // Add the 'experimental' configuration here
  experimental: {
    instrumentationHook: true,
  },

  webpack(config, { isServer }) {
    // Handle Node.js modules and OpenTelemetry packages
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        '@azure/monitor-opentelemetry-exporter':
          '@azure/monitor-opentelemetry-exporter',
        '@opentelemetry/exporter-trace-otlp-http':
          '@opentelemetry/exporter-trace-otlp-http',
      });
    } else {
      // For client-side, ignore these packages completely
      config.resolve = config.resolve || {};
      config.resolve.fallback = {
        ...config.resolve.fallback,
        '@azure/monitor-opentelemetry-exporter': false,
        '@opentelemetry/exporter-trace-otlp-http': false,
      };
    }

    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule) =>
      rule.test?.test?.('.svg')
    );

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: ['@svgr/webpack'],
      }
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

export default nextConfig;
