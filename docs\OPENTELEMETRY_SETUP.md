# OpenTelemetry Setup Guide

This guide explains how to configure OpenTelemetry for the ARCA EMR application across different environments.

## Overview

The application uses **automatic environment detection** based on available configuration:

- **Azure Application Insights**: When `APPLICATIONINSIGHTS_CONNECTION_STRING` is present
- **Grafana Cloud OTLP**: When `GRAFANA_OTLP_ENDPOINT`, `GRAFANA_USER`, and `GRAFANA_API_KEY` are present
- **Development Mode**: When no telemetry configuration is detected (safe for local development)

No manual environment setting required - the system automatically detects the appropriate telemetry provider!

## Environment Configuration

### QA Environment (Vercel Deployment)

1. **Set Environment Variables in Vercel:**

   ```bash
   SERVICE_NAME=arca-emr
   GRAFANA_OTLP_ENDPOINT=https://otlp-gateway-prod-us-central-0.grafana.net/otlp/v1/traces
   GRAFANA_USER=your-grafana-user-id
   GRAFANA_API_KEY=your-grafana-api-key
   ```

2. **Get Grafana Cloud Credentials:**
   - Log into your Grafana Cloud instance
   - Go to "My Account" → "Security" → "API Keys"
   - Create a new API key with "MetricsPublisher" role
   - Note your User ID and API Key

3. **Find Your OTLP Endpoint:**
   - In Grafana Cloud, go to "Connections" → "Add new connection"
   - Search for "OpenTelemetry"
   - Copy the OTLP endpoint URL

### Production Environment (Azure Static Web App)

1. **Set Environment Variables in Azure:**

   ```bash
   SERVICE_NAME=arca-emr
   APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=xxx;IngestionEndpoint=https://xxx.in.applicationinsights.azure.com/;LiveEndpoint=https://xxx.livediagnostics.monitor.azure.com/
   ```

2. **Get Application Insights Connection String:**
   - Go to Azure Portal → Application Insights
   - Select your Application Insights resource
   - Copy the "Connection String" from the Overview page

3. **Configure in Azure Static Web App:**
   - Go to your Static Web App in Azure Portal
   - Navigate to "Configuration" → "Application settings"
   - Add the environment variables

### Development Environment

For local development, simply don't set any telemetry environment variables.

No telemetry will be initialized, keeping your local environment clean and fast.

## Verification

### Check Logs

Look for these console messages when the app starts:

**QA Environment:**

```
🚀 Initializing OpenTelemetry for environment: qa
🟢 Using Grafana Cloud OTLP Exporter for QA/Staging
✅ OpenTelemetry successfully initialized for arca-emr in qa environment
```

**Production Environment:**

```
🚀 Initializing OpenTelemetry for environment: production
🔵 Using Azure Application Insights Exporter for Production
✅ OpenTelemetry successfully initialized for arca-emr in production environment
```

### Troubleshooting

**Missing Configuration:**
If you see warnings like:

```
⚠️ Grafana configuration incomplete for QA environment
⚠️ APPLICATIONINSIGHTS_CONNECTION_STRING not found for production environment
```

Check that all required environment variables are set correctly.

**Initialization Errors:**
If you see:

```
❌ Failed to initialize OpenTelemetry: [error details]
```

The app will continue to work, but telemetry won't be collected. Check your configuration and network connectivity.

## Viewing Telemetry Data

### Grafana (QA Environment)

1. Log into your Grafana Cloud instance
2. Go to "Explore" → Select "Tempo" data source
3. Search for traces from service "arca-emr"

### Azure Application Insights (Production)

1. Go to Azure Portal → Application Insights
2. Select your resource
3. Navigate to "Transaction search" or "Application map"
4. Filter by application name "arca-emr"

## Next Steps

1. Set up alerts and dashboards in both Grafana and Azure Application Insights
2. Configure custom metrics and spans in your application code
3. Set up distributed tracing across your microservices
4. Create performance monitoring dashboards
