// Utility functions to test OpenTelemetry integration
import { trace, SpanStatusCode } from '@opentelemetry/api';

const tracer = trace.getTracer('arca-emr-test');

/**
 * Test function to create a sample trace
 * Call this from your components to verify telemetry is working
 */
export function createTestTrace(operationName: string = 'test-operation') {
  const span = tracer.startSpan(operationName);

  try {
    // Add some attributes to the span
    span.setAttributes({
      'test.environment': process.env.NEXT_PUBLIC_ENV || 'unknown',
      'test.timestamp': new Date().toISOString(),
      'test.user.action': 'manual-test',
    });

    // Simulate some work
    span.addEvent('Test operation started');

    // You can add more events or child spans here
    span.addEvent('Test operation completed');

    span.setStatus({ code: SpanStatusCode.OK });

    console.log(`✅ Test trace created: ${operationName}`);
  } catch (error) {
    span.recordException(error as Error);
    span.setStatus({
      code: SpanStatusCode.ERROR,
      message: (error as Error).message,
    });

    console.error('❌ Error creating test trace:', error);
  } finally {
    span.end();
  }
}

/**
 * Create a test trace with nested spans to test distributed tracing
 */
export function createNestedTestTrace() {
  const parentSpan = tracer.startSpan('parent-operation');

  try {
    parentSpan.setAttributes({
      'operation.type': 'parent',
      'test.nested': true,
    });

    // Create child span within parent context
    const childSpan = tracer.startSpan('child-operation');

    try {
      childSpan.setAttributes({
        'operation.type': 'child',
        'child.data': 'test-data',
      });

      childSpan.addEvent('Child operation processing');

      // Simulate async work
      setTimeout(() => {
        childSpan.addEvent('Child operation async work completed');
        childSpan.setStatus({ code: SpanStatusCode.OK });
        childSpan.end();
      }, 100);

      parentSpan.addEvent('Child span created');
      parentSpan.setStatus({ code: SpanStatusCode.OK });

      console.log('✅ Nested test trace created');
    } catch (error) {
      childSpan.recordException(error as Error);
      childSpan.setStatus({
        code: SpanStatusCode.ERROR,
        message: (error as Error).message,
      });
      childSpan.end();
      throw error;
    }
  } catch (error) {
    parentSpan.recordException(error as Error);
    parentSpan.setStatus({
      code: SpanStatusCode.ERROR,
      message: (error as Error).message,
    });

    console.error('❌ Error creating nested test trace:', error);
  } finally {
    parentSpan.end();
  }
}

/**
 * Test error handling in traces
 */
export function createErrorTestTrace() {
  const span = tracer.startSpan('error-test-operation');

  try {
    span.setAttributes({
      'test.type': 'error-simulation',
      'test.expected': true,
    });

    span.addEvent('Simulating error condition');

    // Simulate an error
    throw new Error('This is a test error for telemetry verification');
  } catch (error) {
    span.recordException(error as Error);
    span.setStatus({
      code: SpanStatusCode.ERROR,
      message: (error as Error).message,
    });

    console.log('✅ Error test trace created (this error is expected)');
  } finally {
    span.end();
  }
}

/**
 * Utility to check if telemetry is properly initialized
 */
export function checkTelemetryStatus() {
  console.log('🔍 Telemetry Status Check:');
  console.log(`Service Name: ${process.env.SERVICE_NAME || 'arca-emr'}`);

  // Check for telemetry configuration (client-side visible variables only)
  const hasGrafanaEndpoint = !!process.env.GRAFANA_OTLP_ENDPOINT;
  const hasGrafanaUser = !!process.env.GRAFANA_USER;
  const hasGrafanaApiKey = !!process.env.GRAFANA_API_KEY;

  console.log('📊 Configuration Detection:');
  console.log(
    `Grafana Endpoint: ${hasGrafanaEndpoint ? '✅ Configured' : '❌ Missing'}`
  );
  console.log(
    `Grafana User: ${hasGrafanaUser ? '✅ Configured' : '❌ Missing'}`
  );
  console.log(
    `Grafana API Key: ${hasGrafanaApiKey ? '✅ Configured' : '❌ Missing'}`
  );
  console.log('Azure App Insights: Check server logs (server-only variable)');

  console.log('\n🤖 Auto-Detection Logic:');
  console.log(
    '• If APPLICATIONINSIGHTS_CONNECTION_STRING exists → Azure Application Insights'
  );
  console.log('• If GRAFANA_* variables exist → Grafana Cloud OTLP');
  console.log('• If neither → No telemetry (development mode)');

  console.log(
    '\n💡 Note: Telemetry is initialized server-side only. Check server console for initialization logs.'
  );
}
