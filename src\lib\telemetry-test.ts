// Utility functions to test OpenTelemetry integration
import { trace, context, SpanStatusCode } from '@opentelemetry/api';

const tracer = trace.getTracer('arca-emr-test');

/**
 * Test function to create a sample trace
 * Call this from your components to verify telemetry is working
 */
export function createTestTrace(operationName: string = 'test-operation') {
  const span = tracer.startSpan(operationName);
  
  try {
    // Add some attributes to the span
    span.setAttributes({
      'test.environment': process.env.NEXT_PUBLIC_ENV || 'unknown',
      'test.timestamp': new Date().toISOString(),
      'test.user.action': 'manual-test',
    });

    // Simulate some work
    span.addEvent('Test operation started');
    
    // You can add more events or child spans here
    span.addEvent('Test operation completed');
    
    span.setStatus({ code: SpanStatusCode.OK });
    
    console.log(`✅ Test trace created: ${operationName}`);
    
  } catch (error) {
    span.recordException(error as Error);
    span.setStatus({ 
      code: SpanStatusCode.ERROR, 
      message: (error as Error).message 
    });
    
    console.error('❌ Error creating test trace:', error);
    
  } finally {
    span.end();
  }
}

/**
 * Create a test trace with nested spans to test distributed tracing
 */
export function createNestedTestTrace() {
  const parentSpan = tracer.startSpan('parent-operation');
  
  try {
    parentSpan.setAttributes({
      'operation.type': 'parent',
      'test.nested': true,
    });

    // Create child span
    const childSpan = tracer.startSpan('child-operation', {
      parent: parentSpan,
    });

    try {
      childSpan.setAttributes({
        'operation.type': 'child',
        'child.data': 'test-data',
      });

      childSpan.addEvent('Child operation processing');
      
      // Simulate async work
      setTimeout(() => {
        childSpan.addEvent('Child operation async work completed');
        childSpan.setStatus({ code: SpanStatusCode.OK });
        childSpan.end();
      }, 100);

      parentSpan.addEvent('Child span created');
      parentSpan.setStatus({ code: SpanStatusCode.OK });
      
      console.log('✅ Nested test trace created');
      
    } catch (error) {
      childSpan.recordException(error as Error);
      childSpan.setStatus({ 
        code: SpanStatusCode.ERROR, 
        message: (error as Error).message 
      });
      childSpan.end();
      throw error;
    }
    
  } catch (error) {
    parentSpan.recordException(error as Error);
    parentSpan.setStatus({ 
      code: SpanStatusCode.ERROR, 
      message: (error as Error).message 
    });
    
    console.error('❌ Error creating nested test trace:', error);
    
  } finally {
    parentSpan.end();
  }
}

/**
 * Test error handling in traces
 */
export function createErrorTestTrace() {
  const span = tracer.startSpan('error-test-operation');
  
  try {
    span.setAttributes({
      'test.type': 'error-simulation',
      'test.expected': true,
    });

    span.addEvent('Simulating error condition');
    
    // Simulate an error
    throw new Error('This is a test error for telemetry verification');
    
  } catch (error) {
    span.recordException(error as Error);
    span.setStatus({ 
      code: SpanStatusCode.ERROR, 
      message: (error as Error).message 
    });
    
    console.log('✅ Error test trace created (this error is expected)');
    
  } finally {
    span.end();
  }
}

/**
 * Utility to check if telemetry is properly initialized
 */
export function checkTelemetryStatus() {
  const env = process.env.NEXT_PUBLIC_ENV || process.env.NODE_ENV;
  
  console.log('🔍 Telemetry Status Check:');
  console.log(`Environment: ${env}`);
  console.log(`Service Name: ${process.env.NEXT_PUBLIC_SERVICE_NAME || 'arca-emr'}`);
  
  if (env === 'production') {
    console.log(`Azure App Insights: ${process.env.APPLICATIONINSIGHTS_CONNECTION_STRING ? '✅ Configured' : '❌ Missing'}`);
  } else if (env === 'qa' || env === 'staging') {
    console.log(`Grafana Endpoint: ${process.env.NEXT_PUBLIC_GRAFANA_OTLP_ENDPOINT ? '✅ Configured' : '❌ Missing'}`);
    console.log(`Grafana User: ${process.env.NEXT_PUBLIC_GRAFANA_USER ? '✅ Configured' : '❌ Missing'}`);
    console.log(`Grafana API Key: ${process.env.NEXT_PUBLIC_GRAFANA_API_KEY ? '✅ Configured' : '❌ Missing'}`);
  } else {
    console.log('ℹ️ Development environment - no telemetry configured');
  }
}
