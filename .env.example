# ============================================
# ARCA EMR - Environment Configuration Example
# ============================================
# Copy this file to `.env` and update the values accordingly
# ============================================


# ================================
# API CONFIGURATION
# ================================

# Frontend application URL
NEXT_PUBLIC_URL=http://localhost:3001

# Backend API URL
NEXT_PUBLIC_API_URL=your-api-url

# API subscription key for Azure API Management
NEXT_PUBLIC_SUBSCRIPTION_KEY=your-subscription-key


# ================================
# SPEECH ENGINE CONFIGURATION
# ================================

# Speech token subscription key
NEXT_PUBLIC_SPEECH_ENGINE_SUBSCRIPTION_KEY=your-subscription-key


# ================================
# AZURE AD B2C CONFIGURATION
# ================================

# Azure AD B2C Client ID
NEXT_PUBLIC_CLIENT_ID=your-client-id

# Azure AD B2C Tenant Name (e.g., yourtenant)
NEXT_PUBLIC_TENANT_NAME=your-tenant-name

# Azure AD B2C Tenant ID (GUID)
NEXT_PUBLIC_TENANT_ID=your-tenant-id

# Azure AD B2C Sign-in Policy (e.g., B2C_1_signin)
NEXT_PUBLIC_SIGNIN_POLICY=your-signin-policy


# ================================
# DEVELOPMENT CONFIGURATION
# ================================

# Enable debug logging or dev-related settings
NEXT_PUBLIC_NODE_ENV=development


# ================================
# OPENTELEMETRY CONFIGURATION
# ================================

# Service name for telemetry (optional, defaults to 'arca-emr')
SERVICE_NAME=arca-emr

# --------------------------------
# GRAFANA CLOUD (QA/Staging - Vercel)
# --------------------------------

# Grafana Cloud OTLP endpoint
# Format: https://otlp-gateway-{region}.grafana.net/otlp/v1/traces
# Example: https://otlp-gateway-prod-us-central-0.grafana.net/otlp/v1/traces
GRAFANA_OTLP_ENDPOINT=https://otlp-gateway-prod-us-central-0.grafana.net/otlp/v1/traces

# Grafana Cloud credentials (get from your Grafana Cloud instance)
GRAFANA_USER=your-grafana-user-id
GRAFANA_API_KEY=your-grafana-api-key

# --------------------------------
# AZURE APPLICATION INSIGHTS (Production - Azure)
# --------------------------------

# Azure Application Insights connection string
# Format: InstrumentationKey=xxx;IngestionEndpoint=https://xxx.in.applicationinsights.azure.com/;LiveEndpoint=https://xxx.livediagnostics.monitor.azure.com/
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=your-key;IngestionEndpoint=https://your-region.in.applicationinsights.azure.com/;LiveEndpoint=https://your-region.livediagnostics.monitor.azure.com/


# ================================
# DEPLOYMENT NOTES
# ================================
#
# QA Environment (Vercel):
# - Configure Grafana variables in Vercel environment settings:
#   GRAFANA_OTLP_ENDPOINT, GRAFANA_USER, GRAFANA_API_KEY
# - Telemetry will auto-detect Vercel deployment
#
# Production Environment (Azure Static Web App):
# - Configure APPLICATIONINSIGHTS_CONNECTION_STRING in Azure app settings
# - Telemetry will auto-detect Azure Application Insights configuration
#
# Development Environment:
# - No environment variables needed
# - No telemetry initialized (safe for local development)
#
# Auto-Detection Logic:
# - If APPLICATIONINSIGHTS_CONNECTION_STRING exists → Use Azure Application Insights
# - If GRAFANA_* variables exist → Use Grafana Cloud OTLP
# - If neither → No telemetry (development mode)
