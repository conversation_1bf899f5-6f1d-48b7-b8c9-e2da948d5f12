# ============================================
# ARCA EMR - Environment Configuration Example
# ============================================
# Copy this file to `.env` and update the values accordingly
# ============================================


# ================================
# API CONFIGURATION
# ================================

# Frontend application URL
NEXT_PUBLIC_URL=http://localhost:3001

# Backend API URL
NEXT_PUBLIC_API_URL=your-api-url

# API subscription key for Azure API Management
NEXT_PUBLIC_SUBSCRIPTION_KEY=your-subscription-key


# ================================
# SPEECH ENGINE CONFIGURATION
# ================================

# Speech token subscription key
NEXT_PUBLIC_SPEECH_ENGINE_SUBSCRIPTION_KEY=your-subscription-key


# ================================
# AZURE AD B2C CONFIGURATION
# ================================

# Azure AD B2C Client ID
NEXT_PUBLIC_CLIENT_ID=your-client-id

# Azure AD B2C Tenant Name (e.g., yourtenant)
NEXT_PUBLIC_TENANT_NAME=your-tenant-name

# Azure AD B2C Tenant ID (GUID)
NEXT_PUBLIC_TENANT_ID=your-tenant-id

# Azure AD B2C Sign-in Policy (e.g., B2C_1_signin)
NEXT_PUBLIC_SIGNIN_POLICY=your-signin-policy


# ================================
# DEVELOPMENT CONFIGURATION
# ================================

# Enable debug logging or dev-related settings
NEXT_PUBLIC_NODE_ENV=development


# ================================
# OPENTELEMETRY CONFIGURATION
# ================================

# Environment setting for telemetry (overrides NODE_ENV)
# Options: development, qa, staging, production
NEXT_PUBLIC_ENV=development

# Service name for telemetry (optional, defaults to 'arca-emr')
NEXT_PUBLIC_SERVICE_NAME=arca-emr

# --------------------------------
# GRAFANA CLOUD (QA/Staging - Vercel)
# --------------------------------

# Grafana Cloud OTLP endpoint
# Format: https://otlp-gateway-{region}.grafana.net/otlp/v1/traces
# Example: https://otlp-gateway-prod-us-central-0.grafana.net/otlp/v1/traces
NEXT_PUBLIC_GRAFANA_OTLP_ENDPOINT=https://otlp-gateway-prod-us-central-0.grafana.net/otlp/v1/traces

# Grafana Cloud credentials (get from your Grafana Cloud instance)
NEXT_PUBLIC_GRAFANA_USER=your-grafana-user-id
NEXT_PUBLIC_GRAFANA_API_KEY=your-grafana-api-key

# --------------------------------
# AZURE APPLICATION INSIGHTS (Production - Azure)
# --------------------------------

# Azure Application Insights connection string
# Format: InstrumentationKey=xxx;IngestionEndpoint=https://xxx.in.applicationinsights.azure.com/;LiveEndpoint=https://xxx.livediagnostics.monitor.azure.com/
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=your-key;IngestionEndpoint=https://your-region.in.applicationinsights.azure.com/;LiveEndpoint=https://your-region.livediagnostics.monitor.azure.com/


# ================================
# DEPLOYMENT NOTES
# ================================
#
# QA Environment (Vercel):
# - Set NEXT_PUBLIC_ENV=qa
# - Configure Grafana variables in Vercel environment settings
# - Use NEXT_PUBLIC_ prefix for client-side access
#
# Production Environment (Azure Static Web App):
# - Set NEXT_PUBLIC_ENV=production
# - Configure APPLICATIONINSIGHTS_CONNECTION_STRING in Azure app settings
# - No NEXT_PUBLIC_ prefix needed for server-side variables
#
# Development Environment:
# - Set NEXT_PUBLIC_ENV=development
# - No telemetry initialized (safe for local development)
