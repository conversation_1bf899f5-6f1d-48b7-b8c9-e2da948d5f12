// instrumentation.ts
import { AzureMonitorTraceExporter } from '@azure/monitor-opentelemetry-exporter';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { registerOTel } from '@vercel/otel';

export async function register() {
  // Determine environment - prioritize NEXT_PUBLIC_ENV for explicit environment setting
  const env = process.env.NEXT_PUBLIC_ENV || process.env.NODE_ENV;
  const serviceName = process.env.NEXT_PUBLIC_SERVICE_NAME || 'arca-emr';

  console.log(`🚀 Initializing OpenTelemetry for environment: ${env}`);

  let traceExporter: any;

  try {
    if (env === 'production') {
      // Azure Static Web App - Use Azure Application Insights
      const connectionString =
        process.env.APPLICATIONINSIGHTS_CONNECTION_STRING;

      if (!connectionString) {
        console.warn(
          '⚠️ APPLICATIONINSIGHTS_CONNECTION_STRING not found for production environment'
        );
        return;
      }

      console.log(
        '🔵 Using Azure Application Insights Exporter for Production'
      );
      traceExporter = new AzureMonitorTraceExporter({
        connectionString,
      });
    } else if (env === 'qa' || env === 'staging') {
      // Vercel deployment - Use Grafana Cloud OTLP
      const otlpEndpoint = process.env.NEXT_PUBLIC_GRAFANA_OTLP_ENDPOINT;
      const grafanaUser = process.env.NEXT_PUBLIC_GRAFANA_USER;
      const grafanaApiKey = process.env.NEXT_PUBLIC_GRAFANA_API_KEY;

      if (!otlpEndpoint || !grafanaUser || !grafanaApiKey) {
        console.warn('⚠️ Grafana configuration incomplete for QA environment');
        console.warn('Missing:', {
          endpoint: !otlpEndpoint,
          user: !grafanaUser,
          apiKey: !grafanaApiKey,
        });
        return;
      }

      console.log('🟢 Using Grafana Cloud OTLP Exporter for QA/Staging');
      traceExporter = new OTLPTraceExporter({
        url: otlpEndpoint,
        headers: {
          Authorization: `Basic ${btoa(`${grafanaUser}:${grafanaApiKey}`)}`,
          'Content-Type': 'application/json',
        },
      });
    } else {
      console.log(
        `⚪ No telemetry exporter configured for environment: ${env}`
      );
      console.log(
        'Supported environments: production (Azure), qa/staging (Grafana)'
      );
      return;
    }

    // Register OpenTelemetry with the appropriate exporter
    await registerOTel({
      serviceName,
      traceExporter,
    });

    console.log(
      `✅ OpenTelemetry successfully initialized for ${serviceName} in ${env} environment`
    );
  } catch (error) {
    console.error('❌ Failed to initialize OpenTelemetry:', error);
    // Don't throw - we don't want telemetry issues to break the app
  }
}
