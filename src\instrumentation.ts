import { AzureMonitorTraceExporter } from '@azure/monitor-opentelemetry-exporter';
import { metrics } from '@opentelemetry/api';
import { OTLPMetricExporter } from '@opentelemetry/exporter-metrics-otlp-http';
import {
  MeterProvider,
  PeriodicExportingMetricReader,
} from '@opentelemetry/sdk-metrics';
import { registerOTel } from '@vercel/otel';

export async function register() {
  // Register with the Azure Monitor exporter.
  registerOTel({
    serviceName: 'your-nextjs-app',
    traceExporter: new AzureMonitorTraceExporter({
      connectionString: process.env.APPLICATIONINSIGHTS_CONNECTION_STRING,
    }),
  });

  // Set up metrics export to Grafana Cloud via OTLP
  if (process.env.OTLP_METRICS_ENDPOINT && process.env.OTLP_METRICS_AUTH) {
    const metricExporter = new OTLPMetricExporter({
      url: process.env.OTLP_METRICS_ENDPOINT,
      headers: {
        Authorization: `Bearer ${process.env.OTLP_METRICS_AUTH}`,
      },
    });

    const meterProvider = new MeterProvider({
      readers: [
        new PeriodicExportingMetricReader({
          exporter: metricExporter,
          exportIntervalMillis: 10000, // Export every 10 seconds
        }),
      ],
    });

    // Note: Instrumentations removed to avoid Next.js bundling issues. Metrics export is set up, but no automatic metrics collection.

    metrics.setGlobalMeterProvider(meterProvider);
  }
}
